<template>
  <view class="splash-container">
    <!-- 背景动画层 -->
    <view class="background-layer">
      <view class="gradient-bg"></view>
      <view class="particle-layer">
        <view v-for="i in 20" :key="i" class="particle" :style="getParticleStyle(i)"></view>
      </view>
      <view class="wave-layer">
        <view class="wave wave-1"></view>
        <view class="wave wave-2"></view>
        <view class="wave wave-3"></view>
      </view>
    </view>

    <!-- 主要内容 -->
    <view class="content-layer">
      <!-- 应用图标和标题 -->
      <view class="app-branding animate-fadeIn">
        <view class="app-icon-container">
          <view class="app-icon">
            <!-- 使用更精美的辐射监测图标 -->
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" fill="none">
              <!-- 外圈 -->
              <circle cx="50" cy="50" r="40" stroke="#00b4d8" stroke-width="2" fill="none" opacity="0.6" stroke-dasharray="5 3">
                <animateTransform attributeName="transform" type="rotate" values="0 50 50;360 50 50" dur="8s" repeatCount="indefinite"/>
              </circle>
              <!-- 中圈 -->
              <circle cx="50" cy="50" r="25" stroke="#00b4d8" stroke-width="3" fill="none" opacity="0.8">
                <animateTransform attributeName="transform" type="rotate" values="360 50 50;0 50 50" dur="6s" repeatCount="indefinite"/>
              </circle>
              <!-- 内圈 -->
              <circle cx="50" cy="50" r="12" fill="#00b4d8" opacity="0.9">
                <animate attributeName="r" values="10;15;10" dur="2s" repeatCount="indefinite"/>
              </circle>
              <!-- 中心点 -->
              <circle cx="50" cy="50" r="4" fill="#ffffff">
                <animate attributeName="opacity" values="0.8;1;0.8" dur="1.5s" repeatCount="indefinite"/>
              </circle>
              <!-- 辐射线 -->
              <g stroke="#00b4d8" stroke-width="2" stroke-linecap="round" opacity="0.7">
                <line x1="50" y1="15" x2="50" y2="25">
                  <animate attributeName="opacity" values="0.5;0.7;0.5" dur="1s" repeatCount="indefinite"/>
                </line>
                <line x1="50" y1="75" x2="50" y2="85">
                  <animate attributeName="opacity" values="0.5;0.7;0.5" dur="1s" repeatCount="indefinite" begin="0.25s"/>
                </line>
                <line x1="15" y1="50" x2="25" y2="50">
                  <animate attributeName="opacity" values="0.5;0.7;0.5" dur="1s" repeatCount="indefinite" begin="0.5s"/>
                </line>
                <line x1="75" y1="50" x2="85" y2="50">
                  <animate attributeName="opacity" values="0.5;0.7;0.5" dur="1s" repeatCount="indefinite" begin="0.75s"/>
                </line>
              </g>
            </svg>
          </view>
          <view class="icon-glow"></view>
        </view>
        
        <view class="app-title-section">
          <text class="app-title">智能辐射监测</text>
          <text class="app-subtitle">专业的智能穿戴核辐射监测应用</text>
          <text class="app-version">v1.0.0</text>
        </view>
      </view>

      <!-- 加载动画 -->
      <view class="loading-section animate-slideUp">
        <view class="loading-container">
          <view class="loading-ring">
            <view class="ring-segment segment-1"></view>
            <view class="ring-segment segment-2"></view>
            <view class="ring-segment segment-3"></view>
            <view class="ring-segment segment-4"></view>
          </view>
          <view class="loading-center">
            <view class="pulse-dot"></view>
          </view>
        </view>
        
        <view class="loading-text-container">
          <text class="loading-text">{{ loadingText }}</text>
          <view class="loading-dots">
            <view class="dot dot-1"></view>
            <view class="dot dot-2"></view>
            <view class="dot dot-3"></view>
          </view>
        </view>
      </view>

      <!-- 底部信息 -->
      <view class="footer-section animate-fadeIn">
        <view class="feature-highlights">
          <view class="feature-item">
            <view class="feature-icon">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
                <path d="M9 12l2 2 4-4"></path>
              </svg>
            </view>
            <text class="feature-text">实时监控</text>
          </view>
          
          <view class="feature-item">
            <view class="feature-icon">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                <polyline points="22 4 12 14.01 9 11.01"></polyline>
              </svg>
            </view>
            <text class="feature-text">智能分析</text>
          </view>
          
          <view class="feature-item">
            <view class="feature-icon">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
                <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
              </svg>
            </view>
            <text class="feature-text">安全预警</text>
          </view>
        </view>
        
        <view class="copyright">
          <text class="copyright-text">© 2024 智能辐射监测系统</text>
          <text class="company-text">科技有限公司</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'

export default {
  name: 'SplashPage',
  setup() {
    const loadingText = ref('正在初始化系统')
    const loadingSteps = [
      '正在初始化系统',
      '正在连接设备',
      '正在加载配置',
      '正在准备界面',
      '即将完成'
    ]
    
    let currentStep = 0
    let loadingInterval = null
    let splashTimer = null

    // 生成粒子样式
    const getParticleStyle = (index) => {
      const size = Math.random() * 6 + 2
      const left = Math.random() * 100
      const animationDelay = Math.random() * 3
      const animationDuration = Math.random() * 2 + 3
      
      return {
        width: `${size}rpx`,
        height: `${size}rpx`,
        left: `${left}%`,
        animationDelay: `${animationDelay}s`,
        animationDuration: `${animationDuration}s`
      }
    }

    // 更新加载文本
    const updateLoadingText = () => {
      loadingText.value = loadingSteps[currentStep]
      currentStep = (currentStep + 1) % loadingSteps.length
    }

    // 跳转到主页面
    const navigateToMain = () => {
      // 添加淡出动画
      const container = document.querySelector('.splash-container')
      if (container) {
        container.style.animation = 'fadeOut 0.5s ease-out forwards'
        setTimeout(() => {
          uni.reLaunch({
            url: '/pages/dashboard/dashboard'
          })
        }, 500)
      } else {
        uni.reLaunch({
          url: '/pages/dashboard/dashboard'
        })
      }
    }

    onMounted(() => {
      // 启动加载文本动画
      loadingInterval = setInterval(updateLoadingText, 800)
      
      // 3秒后跳转到主页面
      splashTimer = setTimeout(() => {
        navigateToMain()
      }, 3000)
    })

    onUnmounted(() => {
      if (loadingInterval) {
        clearInterval(loadingInterval)
      }
      if (splashTimer) {
        clearTimeout(splashTimer)
      }
    })

    return {
      loadingText,
      getParticleStyle
    }
  }
}
</script>

<style scoped>
.splash-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

/* 背景层 */
.background-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.gradient-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, 
    rgba(0, 180, 216, 0.9) 0%, 
    rgba(0, 150, 199, 0.8) 50%, 
    rgba(102, 126, 234, 0.9) 100%);
  animation: gradientShift 6s ease-in-out infinite;
}

@keyframes gradientShift {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

/* 粒子动画 */
.particle-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.particle {
  position: absolute;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  animation: particleFloat linear infinite;
}

@keyframes particleFloat {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100rpx) rotate(360deg);
    opacity: 0;
  }
}

/* 波浪动画 */
.wave-layer {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 200rpx;
  overflow: hidden;
}

.wave {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 200%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  animation: waveMove 4s linear infinite;
}

.wave-1 { animation-delay: 0s; }
.wave-2 { animation-delay: 1s; opacity: 0.7; }
.wave-3 { animation-delay: 2s; opacity: 0.5; }

@keyframes waveMove {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(0%); }
}

/* 内容层 */
.content-layer {
  position: relative;
  z-index: 10;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 120rpx 60rpx 80rpx;
}

/* 应用品牌区域 */
.app-branding {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  margin-top: 100rpx;
}

.app-icon-container {
  position: relative;
  margin-bottom: 60rpx;
}

.app-icon {
  width: 160rpx;
  height: 160rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);
  animation: iconPulse 2s ease-in-out infinite;
  position: relative;
  z-index: 2;
}

.app-icon svg {
  width: 80rpx;
  height: 80rpx;
  color: #00b4d8;
}

.icon-glow {
  position: absolute;
  top: -20rpx;
  left: -20rpx;
  right: -20rpx;
  bottom: -20rpx;
  background: radial-gradient(circle, rgba(0, 180, 216, 0.3) 0%, transparent 70%);
  border-radius: 50rpx;
  animation: glowPulse 2s ease-in-out infinite;
  z-index: 1;
}

@keyframes iconPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes glowPulse {
  0%, 100% { opacity: 0.5; transform: scale(1); }
  50% { opacity: 0.8; transform: scale(1.1); }
}

.app-title-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.app-title {
  font-size: 56rpx;
  font-weight: 700;
  color: #ffffff;
  text-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.3);
  letter-spacing: 2rpx;
  margin-bottom: 8rpx;
}

.app-subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
  line-height: 1.4;
  max-width: 500rpx;
  text-align: center;
}

.app-version {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  margin-top: 16rpx;
  padding: 8rpx 20rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
}

/* 加载区域 */
.loading-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 40rpx;
}

.loading-container {
  position: relative;
  width: 120rpx;
  height: 120rpx;
}

.loading-ring {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.ring-segment {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 6rpx solid transparent;
  border-radius: 50%;
  animation: ringRotate 2s linear infinite;
}

.segment-1 {
  border-top-color: #ffffff;
  animation-delay: 0s;
}

.segment-2 {
  border-right-color: rgba(255, 255, 255, 0.7);
  animation-delay: 0.5s;
}

.segment-3 {
  border-bottom-color: rgba(255, 255, 255, 0.5);
  animation-delay: 1s;
}

.segment-4 {
  border-left-color: rgba(255, 255, 255, 0.3);
  animation-delay: 1.5s;
}

@keyframes ringRotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20rpx;
  height: 20rpx;
}

.pulse-dot {
  width: 100%;
  height: 100%;
  background: #ffffff;
  border-radius: 50%;
  animation: pulseDot 1.5s ease-in-out infinite;
}

@keyframes pulseDot {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.6; transform: scale(1.2); }
}

.loading-text-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
}

.loading-text {
  font-size: 32rpx;
  color: #ffffff;
  font-weight: 500;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

.loading-dots {
  display: flex;
  gap: 8rpx;
}

.dot {
  width: 12rpx;
  height: 12rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  animation: dotBounce 1.4s ease-in-out infinite;
}

.dot-1 { animation-delay: 0s; }
.dot-2 { animation-delay: 0.2s; }
.dot-3 { animation-delay: 0.4s; }

@keyframes dotBounce {
  0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
  40% { transform: scale(1.2); opacity: 1; }
}

/* 底部区域 */
.footer-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 40rpx;
  margin-bottom: 40rpx;
}

.feature-highlights {
  display: flex;
  justify-content: center;
  gap: 60rpx;
  margin-bottom: 20rpx;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
}

.feature-icon {
  width: 48rpx;
  height: 48rpx;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10rpx);
}

.feature-icon svg {
  width: 24rpx;
  height: 24rpx;
  color: #ffffff;
}

.feature-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.3);
}

.copyright {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.copyright-text,
.company-text {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
  text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.3);
}

/* 动画类 */
.animate-fadeIn {
  animation: fadeIn 1s ease-out forwards;
}

.animate-slideUp {
  animation: slideUp 1s ease-out 0.5s forwards;
  opacity: 0;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-30rpx); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideUp {
  from { opacity: 0; transform: translateY(50rpx); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .content-layer {
    padding: 100rpx 40rpx 60rpx;
  }

  .app-title {
    font-size: 48rpx;
  }

  .app-subtitle {
    font-size: 26rpx;
  }

  .feature-highlights {
    gap: 40rpx;
  }
}
</style>
