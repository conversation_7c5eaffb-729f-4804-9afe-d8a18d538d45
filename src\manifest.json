{
    "name" : "智能辐射监测",
    "appid" : "__UNI__CF81990",
    "description" : "专业的智能穿戴核辐射监测应用，实时监控环境辐射水平，保障健康安全",
    "versionName" : "1.0.0",
    "versionCode" : "100",
    "transformPx" : false,
    /* 应用图标配置 */
    "icons" : {
        "72" : "static/icons/app-icon-64.png",
        "96" : "static/icons/app-icon-128.png",
        "128" : "static/icons/app-icon-128.png",
        "144" : "static/icons/app-icon-256.png",
        "152" : "static/icons/app-icon-256.png",
        "192" : "static/icons/app-icon-256.png",
        "512" : "static/icons/app-icon-512.png"
    },
    /* 5+App特有相关 */
    "app-plus" : {
        "usingComponents" : true,
        "nvueStyleCompiler" : "uni-app",
        "compilerVersion" : 3,
        "splashscreen" : {
            "alwaysShowBeforeRender" : false,
            "waiting" : false,
            "autoclose" : true,
            "delay" : 0
        },
        /* 应用图标配置 */
        "icons" : {
            "android" : {
                "hdpi" : "static/icons/app-icon-128.png",
                "xhdpi" : "static/icons/app-icon-256.png",
                "xxhdpi" : "static/icons/app-icon-512.png"
            },
            "ios" : {
                "appstore" : "static/icons/app-icon-1024.png",
                "ipad" : {
                    "app" : "static/icons/app-icon-128.png",
                    "app@2x" : "static/icons/app-icon-256.png"
                },
                "iphone" : {
                    "app@2x" : "static/icons/app-icon-256.png",
                    "app@3x" : "static/icons/app-icon-512.png"
                }
            }
        },
        /* 模块配置 */
        "modules" : {
            "Geolocation" : {},
            "Maps" : {}
        },
        /* 应用发布信息 */
        "distribute" : {
            /* android打包配置 */
            "android" : {
                "permissions" : [
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
                ]
            },
            /* ios打包配置 */
            "ios" : {
                "dSYMs" : false
            },
            /* SDK配置 */
            "sdkConfigs" : {
                "maps" : {},
                "geolocation" : {
                    "system" : {
                        "__platform__" : [ "ios", "android" ]
                    }
                }
            }
        },
        "uniStatistics" : {
            "enable" : false
        }
    },
    /* 快应用特有相关 */
    "quickapp" : {},
    /* 小程序特有相关 */
    "mp-weixin" : {
        "appid" : "",
        "setting" : {
            "urlCheck" : false
        },
        "usingComponents" : true,
        "permission" : {
            "scope.userLocation" : {
                "desc" : "您的位置信息将用于辐射监测数据的地理标记"
            }
        }
    },
    "mp-alipay" : {
        "usingComponents" : true
    },
    "mp-baidu" : {
        "usingComponents" : true
    },
    "mp-toutiao" : {
        "usingComponents" : true
    },
    "uniStatistics" : {
        "enable" : false,
        "version" : "2"
    },
    "h5" : {
        "sdkConfigs" : {
            "maps" : {
                "amap" : {
                    "key" : "your_web_key_here",
                    "securityJsCode" : "your_security_code_here"
                }
            }
        }
    },
    "vueVersion" : "3"
}
